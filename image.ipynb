{"cells": [{"cell_type": "code", "execution_count": 14, "id": "b08fa3de", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['seil power plant']\n"]}], "source": ["from pathlib import Path\n", "\n", "file_path = Path(\"power_plant.txt\")\n", "with file_path.open(\"r\", encoding=\"utf-8\") as f:\n", "    names = [line.strip() for line in f if line.strip()]\n", "\n", "print(names)\n"]}, {"cell_type": "markdown", "id": "c661ae74", "metadata": {}, "source": ["# URL  SEARCH AND CLEANER"]}, {"cell_type": "code", "execution_count": 16, "id": "3a36d5d7", "metadata": {}, "outputs": [], "source": ["from googlesearch import search\n", "import requests\n", "import logging\n", "from urllib.parse import urlparse\n", "\n", "def is_google_link(url):\n", "    \"\"\"Return True if the URL is a Google-related domain.\"\"\"\n", "    parsed = urlparse(url)\n", "    domain = parsed.netloc.lower()\n", "    return \"google.\" in domain or \"webcache.\" in domain\n", "\n", "\n", "def test_link(url):\n", "    \"\"\"Check if a URL is valid, complete (with scheme and netloc), and not a Google link.\"\"\"\n", "    try:\n", "        parsed = urlparse(url)\n", "        if parsed.scheme not in ('http', 'https'):\n", "            return False\n", "        if not parsed.netloc:\n", "            return False\n", "        if is_google_link(url):\n", "            return False\n", "        return True\n", "    except Exception:\n", "        return False\n", "\n", "def get_valid_links(name, max_results=3):\n", "\n", "    \"\"\"Search for the power plant and return up to 3 valid links.\"\"\"\n", "    keywords = [\"power\", \"plant\", \"station\"]\n", "\n", "    if any(keyword in name.lower() for keyword in keywords):\n", "        query = f\"{name} official site\"\n", "    else:\n", "        query = f\"{name} power station/plant official site\"\n", "\n", "    valid_links = []\n", "\n", "    try:\n", "        results = search(query)  # Get more to filter later\n", "\n", "        for url in results:\n", "            if test_link(url):\n", "                valid_links.append(url)\n", "                if len(valid_links) >= max_results:\n", "                    break\n", "\n", "        if not valid_links:\n", "            print(\"No valid links found.\")\n", "            return None\n", "        return valid_links\n", "\n", "    except Exception as e:\n", "        logging.error(f\"Error during search for '{query}': {e}\")\n", "        return None\n"]}, {"cell_type": "code", "execution_count": 17, "id": "ee63f94c", "metadata": {}, "outputs": [], "source": ["for name in names[:10]:\n", "    urls= get_valid_links(name)"]}, {"cell_type": "markdown", "id": "aa63db82", "metadata": {}, "source": ["# IMAGE SCRAPER"]}, {"cell_type": "code", "execution_count": 18, "id": "d79a72ba", "metadata": {}, "outputs": [], "source": ["#Image_scrap.py \n", "'''\n", "This python file is use to scrap all the images in  a website.\n", "\n", "'''\n", "import os\n", "import logging\n", "import requests\n", "import mimetypes\n", "from bs4 import BeautifulSoup\n", "from urllib.parse import urljoin, urlparse\n", "from requests.adapters import HTTPAdapter\n", "from urllib3.util.retry import Retry\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Create a requests session with retry logic\n", "def get_retry_session():\n", "    session = requests.Session()\n", "    retries = Retry(\n", "        total=3,\n", "        backoff_factor=1,\n", "        status_forcelist=[429, 500, 502, 503, 504],\n", "        raise_on_status=False\n", "    )\n", "    adapter = HTTPAdapter(max_retries=retries)\n", "    session.mount('http://', adapter)\n", "    session.mount('https://', adapter)\n", "    return session\n", "\n", "# Function to scrape and download images\n", "def get_images(base_url, LINKS, image_folder):\n", "    if not LINKS:\n", "        logger.warning(\"⚠️ No links found in the list.\")\n", "        return\n", "\n", "    if not os.path.exists(image_folder):\n", "        os.makedirs(image_folder)\n", "\n", "    session = get_retry_session()\n", "    downloaded_images = set()\n", "\n", "    for count, link in enumerate(LINKS, start=1):\n", "        try:\n", "            headers = {\n", "                \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) \"\n", "                              \"AppleWebKit/537.36 (KHTML, like Gecko) \"\n", "                              \"Chrome/115.0.0.0 Safari/537.36\"\n", "            }\n", "            response = session.get(link, headers=headers, timeout=10)\n", "            if response.status_code != 200:\n", "                logger.error(f\"❌ Failed to retrieve page: {link} (Status {response.status_code})\")\n", "                continue\n", "        except Exception as e:\n", "            logger.error(f\"❌ Failed to request page {link}: {e}\")\n", "            continue\n", "\n", "        soup = BeautifulSoup(response.text, \"html.parser\")\n", "        img_tags = soup.find_all('img')\n", "        logger.info(f\"\\n🔗 Scraping Link {count}: {link}\")\n", "        logger.info(f\"🖼️ Found {len(img_tags)} images\")\n", "\n", "        for img in img_tags:\n", "            img_url = img.get('src')\n", "            if not img_url:\n", "                continue\n", "\n", "            img_url = urljoin(base_url, img_url.split('?')[0].split('#')[0])\n", "\n", "            if img_url in downloaded_images:\n", "                logger.info(f\"🔁 Skipping duplicate: {img_url}\")\n", "                continue\n", "\n", "            if img_url.lower().endswith('.svg'):\n", "                logger.info(f\"🚫 Skipping SVG (by extension): {img_url}\")\n", "                continue\n", "\n", "            # Retry download for image\n", "            for attempt in range(3):\n", "                try:\n", "                    img_resp = session.get(img_url, headers=headers, timeout=10)\n", "                    img_resp.raise_for_status()\n", "\n", "                    content_type = img_resp.headers.get('Content-Type', '').split(';')[0].strip()\n", "                    if content_type == 'image/svg+xml':\n", "                        logger.info(f\"🚫 Skipping SVG (by content-type): {img_url}\")\n", "                        break\n", "\n", "                    # Determine filename\n", "                    filename = os.path.basename(urlparse(img_url).path)\n", "                    if not filename or '.' not in filename:\n", "                        extension = mimetypes.guess_extension(content_type) or '.jpg'\n", "                        filename = f\"image_{count}_{len(downloaded_images)}{extension}\"\n", "\n", "                    filepath = os.path.join(image_folder, filename)\n", "\n", "                    # Avoid filename collision\n", "                    base, ext = os.path.splitext(filename)\n", "                    i = 1\n", "                    while os.path.exists(filepath):\n", "                        filename = f\"{base}_{i}{ext}\"\n", "                        filepath = os.path.join(image_folder, filename)\n", "                        i += 1\n", "\n", "                    with open(filepath, 'wb') as f:\n", "                        f.write(img_resp.content)\n", "\n", "                    downloaded_images.add(img_url)\n", "                    logger.info(f\"✅ Saved: {filename}\")\n", "                    break  # success, break retry loop\n", "\n", "                except Exception as e:\n", "                    logger.warning(f\"⚠️ Attempt {attempt + 1} failed for {img_url}: {e}\")\n", "                    if attempt == 2:\n", "                        logger.error(f\"❌ Failed to download after 3 attempts: {img_url}\")\n", "\n", "\n", "\n", "def image_scaper(name, urls,image_folder):\n", "    \n", "\n", "    for url in urls:\n", "        if not test_link(url):  # Optional: add link validation\n", "            print(f\"⚠️ Skipping invalid URL: {url}\")\n", "            continue\n", "\n", "        parsed_url = urlparse(url)\n", "        base_url = f\"{parsed_url.scheme}://{parsed_url.netloc}\"\n", "        \n", "        # Pass a list containing the original URL, not the parsed object\n", "        get_images(base_url, [url], image_folder)\n", "\n", "    print(f\"✅ Image download completed for {name}\")\n", "\n"]}, {"cell_type": "markdown", "id": "4f9319ed", "metadata": {}, "source": ["# IMAGE FILTER"]}, {"cell_type": "code", "execution_count": 7, "id": "ab9e05e5", "metadata": {}, "outputs": [], "source": ["\n", "import mediapipe as mp\n", "import cv2 \n", "import os\n", "from matplotlib import pyplot as plt\n", "\n", "def face_remove(images_folder):\n", "    image_files=os.listdir(images_folder)\n", "\n", "    print(len(image_files))\n", "    for i in image_files:\n", "        print(i)\n", "\n", "    mp_face = mp.solutions.face_detection\n", "    mp_drawing = mp.solutions.drawing_utils\n", "\n", "    with mp_face.FaceDetection(model_selection=1, min_detection_confidence=0.5) as face_detection:\n", "        for image_link in image_files:\n", "            image_path = os.path.join(images_folder, image_link)\n", "            img = cv2.imread(image_path)\n", "            image_valid=True\n", "            if img is None:\n", "                image_valid=False\n", "                print(\"Something wrong with the image\")\n", "            else:\n", "                if image_valid:\n", "                    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\n", "                    results = face_detection.process(img_rgb)\n", "\n", "                    if not results.detections:\n", "                        print(f\"⚠️ No face found in {image_path}\")\n", "                    else:\n", "                        print(f\"✅ {len(results.detections)} face(s) detected in {image_path}\")\n", "                        for detection in results.detections:\n", "                            mp_drawing.draw_detection(img, detection)\n", "\n", "                        print(\"Deleting the image\")\n", "                        os.remove(image_path) \n", "import easyocr\n", "import os\n", "import cv2\n", "\n", "\n", "def text_remove(images_folder, confidence_threshold=0.5):\n", "    reader = easyocr.Reader(['en'])\n", "    image_files = os.listdir(images_folder)\n", "\n", "    for image_link in image_files:\n", "        image_path = os.path.join(images_folder, image_link)\n", "        img = cv2.imread(image_path)\n", "\n", "        if img is None:\n", "            print(f\"❌ Unable to load image: {image_path}\")\n", "            continue\n", "\n", "        results = reader.readtext(image_path)\n", "\n", "        if not results:\n", "            print(f\"✅ No text found in: {image_path}\")\n", "        else:\n", "            high_conf_texts = [\n", "                (text, conf) for _, text, conf in results if conf >= confidence_threshold\n", "            ]\n", "\n", "            if high_conf_texts:\n", "                print(f\"📄 Text found in: {image_path}\")\n", "                for text, conf in high_conf_texts:\n", "                    print(f\"📝 '{text}' (Confidence: {conf:.2f})\")\n", "                print(f\"🗑️ Deleting image with high-confidence text: {image_path}\")\n", "                os.remove(image_path)\n", "            else:\n", "                print(f\"🧐 Only low-confidence text in: {image_path} — keeping image.\")\n", "\n", "\n", "import os\n", "from PIL import Image\n", "\n", "def remove_small_images(folder_path):\n", "    \"\"\"\n", "    Removes images in the given folder with width or height less than 200 pixels.\n", "    \n", "    Args:\n", "        folder_path (str): Path to the folder containing images.\n", "    \"\"\"\n", "    if not os.path.isdir(folder_path):\n", "        print(f\"Error: {folder_path} is not a valid directory.\")\n", "        return\n", "\n", "    for filename in os.listdir(folder_path):\n", "        file_path = os.path.join(folder_path, filename)\n", "\n", "        if os.path.isfile(file_path):\n", "            try:\n", "                with Image.open(file_path) as img:\n", "                    width, height = img.size\n", "                    if width < 150 or height < 150:\n", "                        os.remove(file_path)\n", "                        print(f\"Removed: {filename} ({width}x{height})\")\n", "            except Exception as e:\n", "                print(f\"Skipping {filename}: {e}\")\n", "\n", "import os\n", "\n", "def remove_non_image_files(folder_path, allowed_extensions=None):\n", "    \"\"\"\n", "    Removes all files from the folder that do not have image extensions.\n", "\n", "    Args:\n", "        folder_path (str): Path to the folder to scan.\n", "        allowed_extensions (set or list): Optional custom set of allowed image extensions.\n", "    \"\"\"\n", "    if not os.path.isdir(folder_path):\n", "        print(f\"Error: {folder_path} is not a valid directory.\")\n", "        return\n", "\n", "    # Default allowed image extensions (case-insensitive)\n", "    if allowed_extensions is None:\n", "        allowed_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}\n", "\n", "    allowed_extensions = {ext.lower() for ext in allowed_extensions}\n", "\n", "    for filename in os.listdir(folder_path):\n", "        file_path = os.path.join(folder_path, filename)\n", "\n", "        if os.path.isfile(file_path):\n", "            _, ext = os.path.splitext(filename)\n", "            if ext.lower() not in allowed_extensions:\n", "                try:\n", "                    os.remove(file_path)\n", "                    print(f\"Removed non-image file: {filename}\")\n", "                except Exception as e:\n", "                    print(f\"Error removing {filename}: {e}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 19, "id": "78223aac", "metadata": {}, "outputs": [], "source": ["def total_filter(image_folder):\n", "    remove_non_image_files(image_folder)\n", "    print(\"🧹 Non-image files removed ✅\")\n", "    \n", "    remove_small_images(image_folder)\n", "    print(\"📏 Small images (less than 200px) removed ✅\")\n", "    \n", "    text_remove(image_folder)\n", "    print(\"📝 Images with text removed ✅\")\n", "    \n", "    face_remove(image_folder)\n", "    print(\"👤 Images with faces removed ✅\")\n", "\n", "\n", "    "]}, {"cell_type": "markdown", "id": "d50ef0e7", "metadata": {}, "source": ["# MAIN FUNCTION"]}, {"cell_type": "code", "execution_count": 10, "id": "f51ae655", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "def main_plant_image(name):\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(f\"📁 Processing: {name}\")\n", "    print(\"=\"*60)\n", "\n", "    image_folder = f'downloads/{name}'\n", "    os.makedirs(image_folder, exist_ok=True)\n", "\n", "    print(\"🔗 Fetching image URLs...\")\n", "    urls = get_valid_links(name)\n", "    print(f\"✅ Found {len(urls)} URLs for '{name}'\")\n", "\n", "    print(\"📥 Starting image scraping...\")\n", "    image_scaper(name, urls, image_folder)\n", "    print(\"✅ Image scraping completed.\")\n", "\n", "    print(\"🧹 Starting filtering process...\")\n", "    total_filter(image_folder)\n", "    print(f\"✅ Filtering completed for '{name}'\")\n", "\n", "    print(\"=\"*60)\n", "    print(f\"🎉 Done processing: {name}\")\n", "    print(\"=\"*60 + \"\\n\")\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 23, "id": "0b0d96e1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "📁 Processing: seil power plant\n", "============================================================\n", "🔗 Fetching image URLs...\n", "✅ Found 3 URLs for 'seil power plant'\n", "📥 Starting image scraping...\n", "✅ Image download completed for seil power plant\n", "✅ Image scraping completed.\n", "🧹 Starting filtering process...\n", "🧹 Non-image files removed ✅\n", "Removed: Recent-News-Slider2.jpg (380x140)\n", "📏 Small images (less than 200px) removed ✅\n", "✅ No text found in: downloads/seil power plant/Business-dropdown.jpg\n", "✅ No text found in: downloads/seil power plant/ThermalPlant.jpg\n", "✅ No text found in: downloads/seil power plant/AboutUs.png\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/dataloader.py:683: UserWarning: 'pin_memory' argument is set as true but not supported on MPS now, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📄 Text found in: downloads/seil power plant/SEIL-Logo_2.png\n", "📝 'SEIL' (Confidence: 1.00)\n", "📝 'SEIL Energy India Limited' (Confidence: 0.94)\n", "📝 '(Formerly Sembcorp Energy India Limited)' (Confidence: 0.93)\n", "🗑️ Deleting image with high-confidence text: downloads/seil power plant/SEIL-Logo_2.png\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/dataloader.py:683: UserWarning: 'pin_memory' argument is set as true but not supported on MPS now, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📄 Text found in: downloads/seil power plant/Website banner_GPTW.jpg\n", "📝 'We are a' (Confidence: 0.87)\n", "📝 'Great Place' (Confidence: 0.87)\n", "📝 'To Work' (Confidence: 0.93)\n", "📝 '#ProudlySEIL' (Confidence: 1.00)\n", "📝 'Great' (Confidence: 1.00)\n", "📝 'Place' (Confidence: 1.00)\n", "📝 'To' (Confidence: 1.00)\n", "📝 'Worka' (Confidence: 0.87)\n", "📝 'Certified' (Confidence: 1.00)\n", "🗑️ Deleting image with high-confidence text: downloads/seil power plant/Website banner_GPTW.jpg\n", "📄 Text found in: downloads/seil power plant/SEIL-Logo_1.png\n", "📝 'SEIL' (Confidence: 1.00)\n", "📝 'SEIL Energy India Limited' (Confidence: 0.94)\n", "📝 '(Formerly Sembcorp Energy India Limited)' (Confidence: 0.93)\n", "🗑️ Deleting image with high-confidence text: downloads/seil power plant/SEIL-Logo_1.png\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/dataloader.py:683: UserWarning: 'pin_memory' argument is set as true but not supported on MPS now, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ No text found in: downloads/seil power plant/investor_relations.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/dataloader.py:683: UserWarning: 'pin_memory' argument is set as true but not supported on MPS now, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📄 Text found in: downloads/seil power plant/Website banner_GPTW_R.jpg\n", "📝 'We are a' (Confidence: 0.61)\n", "📝 'Great Place' (Confidence: 0.91)\n", "📝 'To Work' (Confidence: 0.93)\n", "📝 '#ProudlySEIL' (Confidence: 1.00)\n", "📝 'Great' (Confidence: 1.00)\n", "📝 'Place' (Confidence: 1.00)\n", "📝 'To' (Confidence: 1.00)\n", "📝 'Work' (Confidence: 1.00)\n", "📝 'Certified' (Confidence: 1.00)\n", "🗑️ Deleting image with high-confidence text: downloads/seil power plant/Website banner_GPTW_R.jpg\n", "✅ No text found in: downloads/seil power plant/ThermalPlant_1.jpg\n", "✅ No text found in: downloads/seil power plant/ThermalPlant_2.jpg\n", "✅ No text found in: downloads/seil power plant/CSRMenu.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/dataloader.py:683: UserWarning: 'pin_memory' argument is set as true but not supported on MPS now, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📄 Text found in: downloads/seil power plant/Banner-Annual-Report.png\n", "📝 'LEADING' (Confidence: 1.00)\n", "📝 'RESPONSIBLY' (Confidence: 0.86)\n", "📝 'THRIVING' (Confidence: 0.97)\n", "📝 'SUSTAINABLY' (Confidence: 0.76)\n", "📝 '2024' (Confidence: 1.00)\n", "🗑️ Deleting image with high-confidence text: downloads/seil power plant/Banner-Annual-Report.png\n", "✅ No text found in: downloads/seil power plant/Business-dropdown_2.jpg\n", "✅ No text found in: downloads/seil power plant/CSRMenu_1.jpg\n", "✅ No text found in: downloads/seil power plant/investor_relations_2.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/dataloader.py:683: UserWarning: 'pin_memory' argument is set as true but not supported on MPS now, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📄 Text found in: downloads/seil power plant/Home-banner1.jpg\n", "📝 'Providing essential energy solutions' (Confidence: 0.98)\n", "📝 'to power India's growth' (Confidence: 0.99)\n", "🗑️ Deleting image with high-confidence text: downloads/seil power plant/Home-banner1.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/dataloader.py:683: UserWarning: 'pin_memory' argument is set as true but not supported on MPS now, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📄 Text found in: downloads/seil power plant/Recent-News-Slider1.png\n", "📝 '2,640 MW' (Confidence: 0.74)\n", "📝 'power generation capacity' (Confidence: 0.69)\n", "🗑️ Deleting image with high-confidence text: downloads/seil power plant/Recent-News-Slider1.png\n", "✅ No text found in: downloads/seil power plant/Business-dropdown_1.jpg\n", "✅ No text found in: downloads/seil power plant/CSRMenu_2.jpg\n", "✅ No text found in: downloads/seil power plant/investor_relations_1.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/dataloader.py:683: UserWarning: 'pin_memory' argument is set as true but not supported on MPS now, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📄 Text found in: downloads/seil power plant/SEIL-Logo.png\n", "📝 'SEIL' (Confidence: 1.00)\n", "📝 'SEIL Energy India Limited' (Confidence: 0.94)\n", "📝 '(Formerly Sembcorp Energy India Limited)' (Confidence: 0.93)\n", "🗑️ Deleting image with high-confidence text: downloads/seil power plant/SEIL-Logo.png\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/dataloader.py:683: UserWarning: 'pin_memory' argument is set as true but not supported on MPS now, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/Users/<USER>/miniconda3/lib/python3.12/site-packages/torch/utils/data/dataloader.py:683: UserWarning: 'pin_memory' argument is set as true but not supported on MPS now, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📄 Text found in: downloads/seil power plant/Home-static1.jpg\n", "📝 'Partner with our communities' (Confidence: 0.82)\n", "📝 'for a better tomorrow' (Confidence: 0.96)\n", "🗑️ Deleting image with high-confidence text: downloads/seil power plant/Home-static1.jpg\n", "📄 Text found in: downloads/seil power plant/Home-static2.jpg\n", "📝 'Supporting India's growth' (Confidence: 0.71)\n", "🗑️ Deleting image with high-confidence text: downloads/seil power plant/Home-static2.jpg\n", "📝 Images with text removed ✅\n", "13\n", "Business-dropdown.jpg\n", "ThermalPlant.jpg\n", "AboutUs.png\n", "investor_relations.jpg\n", "ThermalPlant_1.jpg\n", "ThermalPlant_2.jpg\n", "CSRMenu.jpg\n", "Business-dropdown_2.jpg\n", "CSRMenu_1.jpg\n", "investor_relations_2.jpg\n", "Business-dropdown_1.jpg\n", "CSRMenu_2.jpg\n", "investor_relations_1.jpg\n", "⚠️ No face found in downloads/seil power plant/Business-dropdown.jpg\n", "⚠️ No face found in downloads/seil power plant/ThermalPlant.jpg\n", "⚠️ No face found in downloads/seil power plant/AboutUs.png\n", "⚠️ No face found in downloads/seil power plant/investor_relations.jpg\n", "⚠️ No face found in downloads/seil power plant/ThermalPlant_1.jpg\n", "⚠️ No face found in downloads/seil power plant/ThermalPlant_2.jpg\n", "✅ 3 face(s) detected in downloads/seil power plant/CSRMenu.jpg\n", "Deleting the image\n", "⚠️ No face found in downloads/seil power plant/Business-dropdown_2.jpg\n", "✅ 3 face(s) detected in downloads/seil power plant/CSRMenu_1.jpg\n", "Deleting the image\n", "⚠️ No face found in downloads/seil power plant/investor_relations_2.jpg\n", "⚠️ No face found in downloads/seil power plant/Business-dropdown_1.jpg\n", "✅ 3 face(s) detected in downloads/seil power plant/CSRMenu_2.jpg\n", "Deleting the image\n", "⚠️ No face found in downloads/seil power plant/investor_relations_1.jpg\n", "👤 Images with faces removed ✅\n", "✅ Filtering completed for 'seil power plant'\n", "============================================================\n", "🎉 Done processing: seil power plant\n", "============================================================\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: All log messages before absl::InitializeLog() is called are written to STDERR\n", "I0000 00:00:1752668160.053978  783156 gl_context.cc:369] GL version: 2.1 (2.1 Metal - 88.1), renderer: Apple M2\n", "INFO: Created TensorFlow Lite XNNPACK delegate for CPU.\n", "W0000 00:00:1752668160.101314  794674 inference_feedback_manager.cc:114] Feedback manager requires a model with a single signature inference. Disabling support for feedback tensors.\n"]}], "source": ["\n", "\n", "main_plant_image(\"seil power plant\")"]}, {"cell_type": "code", "execution_count": 21, "id": "b8fa69dd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Random image saved as 'random_image.jpg'\n"]}], "source": ["from PIL import Image\n", "import random\n", "\n", "def create_random_image(path=\"random_image.jpg\", width=150, height=150):\n", "    image = Image.new(\"RGB\", (width, height))\n", "    pixels = image.load()\n", "\n", "    for x in range(width):\n", "        for y in range(height):\n", "            # Generate random RGB color\n", "            r = random.randint(0, 255)\n", "            g = random.randint(0, 255)\n", "            b = random.randint(0, 255)\n", "            pixels[x, y] = (r, g, b)\n", "\n", "    image.save(path)\n", "    print(f\"✅ Random image saved as '{path}'\")\n", "\n", "# Example usage\n", "create_random_image()\n"]}, {"cell_type": "code", "execution_count": null, "id": "7417d02c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}