"""
Multi-Plant Extraction Module

This module handles the sequential extraction of data for multiple plants
within an organization, running the full 3-level extraction (org → plant → units)
for each plant in the database.
"""

import logging
from typing import Dict, List, Any

try:
    from database_manager import get_database_manager
except ImportError:
    try:
        from .database_manager import get_database_manager
    except ImportError:
        from src.agent.database_manager import get_database_manager

try:
    from json_s3_storage import store_hierarchical_plant_data
except ImportError:
    try:
        from .json_s3_storage import store_hierarchical_plant_data
    except ImportError:
        from src.agent.json_s3_storage import store_hierarchical_plant_data

logger = logging.getLogger(__name__)

def run_multi_plant_extraction_for_org(org_uid: str, session_id: str) -> Dict[str, Any]:
    """
    SIMPLIFIED Multi-Plant Extraction - No Recursion

    PIPELINE:
    1. Extract organization data ONCE at the beginning
    2. For each plant: extract plant + unit data + images
    3. Save all to S3 hierarchically
    4. Return results

    Args:
        org_uid: Organization UID to process plants for
        session_id: Current session ID for logging

    Returns:
        Dict containing success status, results, and summary
    """
    try:
        print(f"[Session {session_id}] 🚀 SIMPLIFIED multi-plant extraction for org: {org_uid}")

        # Get database manager
        db_manager = get_database_manager()

        # Get all plants for this organization
        plants = db_manager.get_plants_by_organization_uid(org_uid)

        if not plants:
            return {
                "success": False,
                "error": f"No plants found for organization UID: {org_uid}",
                "results": [],
                "plants_processed": 0,
                "successful_extractions": 0,
                "failed_extractions": 0,
                "summary": "No plants to process"
            }

        print(f"[Session {session_id}] 🏭 Found {len(plants)} plants to process")

        # STEP 1: Extract organization data ONCE at the beginning
        print(f"[Session {session_id}] 🏢 Step 1: Extracting organization data (ONCE)...")
        org_name = plants[0].get('org_name', 'Unknown Organization')
        org_s3_url = extract_organization_data_once(org_uid, org_name, plants[0], session_id)

        # STEP 2: Process each plant sequentially (plant + units + images)
        print(f"[Session {session_id}] 🔄 Step 2: Processing plants sequentially...")

        results = []
        successful_extractions = 0
        failed_extractions = 0

        # Process each plant
        for i, plant in enumerate(plants, 1):
            plant_name = plant.get('plant_name', 'Unknown Plant')
            plant_uuid = plant.get('plant_uuid', '')

            print(f"[Session {session_id}] 🔄 Processing plant {i}/{len(plants)}: {plant_name}")

            try:
                # Process this plant (plant + units + images) - NO RECURSION
                plant_result = process_single_plant_simple(
                    plant=plant,
                    org_uid=org_uid,
                    org_name=org_name,
                    session_id=f"{session_id}_plant_{i}"
                )

                if plant_result.get('success', False):
                    successful_extractions += 1
                    print(f"[Session {session_id}] ✅ Plant {plant_name} processed successfully")
                else:
                    failed_extractions += 1
                    print(f"[Session {session_id}] ❌ Plant {plant_name} processing failed")

                results.append(plant_result)

            except Exception as plant_error:
                failed_extractions += 1
                error_result = {
                    "plant_name": plant_name,
                    "plant_uuid": plant_uuid,
                    "success": False,
                    "error": str(plant_error),
                    "processing_order": i,
                    "session_id": f"{session_id}_plant_{i}"
                }
                results.append(error_result)
                print(f"[Session {session_id}] ❌ Exception processing plant {plant_name}: {plant_error}")

        # Generate summary
        summary = f"Processed {len(plants)} plants, {successful_extractions} successful, {failed_extractions} failed"

        print(f"[Session {session_id}] 📊 Multi-plant extraction complete: {summary}")

        return {
            "success": True,
            "results": results,
            "plants_processed": len(plants),
            "successful_extractions": successful_extractions,
            "failed_extractions": failed_extractions,
            "summary": summary,
            "org_uid": org_uid,
            "org_s3_url": org_s3_url,
            "session_id": session_id
        }

    except Exception as e:
        error_msg = f"Multi-plant extraction failed: {str(e)}"
        print(f"[Session {session_id}] ❌ {error_msg}")
        logger.error(f"Multi-plant extraction error for org {org_uid}: {e}")

        return {
            "success": False,
            "error": error_msg,
            "results": [],
            "plants_processed": 0,
            "successful_extractions": 0,
            "failed_extractions": 0,
            "summary": "Extraction failed"
        }


def get_organization_plants_summary(org_uid: str) -> Dict[str, Any]:
    """
    Get a summary of all plants for an organization
    
    Args:
        org_uid: Organization UID
        
    Returns:
        Dict containing plant summary information
    """
    try:
        db_manager = get_database_manager()
        plants = db_manager.get_plants_by_organization_uid(org_uid)
        
        if not plants:
            return {
                "org_uid": org_uid,
                "plant_count": 0,
                "plants": [],
                "countries": [],
                "technologies": []
            }
        
        # Extract unique countries and technologies
        countries = list(set(plant.get('country', 'Unknown') for plant in plants))
        
        # Create plant summaries
        plant_summaries = []
        for plant in plants:
            plant_summaries.append({
                "plant_name": plant.get('plant_name', 'Unknown'),
                "plant_uuid": plant.get('plant_uuid', ''),
                "country": plant.get('country', 'Unknown'),
                "status": plant.get('plant_status', 'operational'),
                "discovery_status": plant.get('discovery_status', 'complete')
            })
        
        return {
            "org_uid": org_uid,
            "plant_count": len(plants),
            "plants": plant_summaries,
            "countries": countries,
            "summary": f"{len(plants)} plants across {len(countries)} countries"
        }
        
    except Exception as e:
        logger.error(f"Error getting organization plants summary: {e}")
        return {
            "org_uid": org_uid,
            "plant_count": 0,
            "plants": [],
            "countries": [],
            "error": str(e)
        }


def extract_organization_data_once(org_uid: str, org_name: str, sample_plant: dict, session_id: str) -> str:
    """
    Extract organization data ONCE and save to S3

    Args:
        org_uid: Organization UID
        org_name: Organization name
        sample_plant: Sample plant for metadata
        session_id: Session ID

    Returns:
        S3 URL of organization JSON
    """
    try:
        # Create organization data
        org_data = {
            "sk": f"org#{org_name.lower().replace(' ', '_')}",
            "org_name": org_name,
            "org_uid": org_uid,
            "country": sample_plant.get('country', 'Unknown'),
            "extraction_type": "multi_plant_organization",
            "timestamp": f"{__import__('datetime').datetime.now().isoformat()}Z"
        }

        # Prepare fallback metadata
        fallback_metadata = {
            "country": sample_plant.get('country', 'Unknown'),
            "org_uuid": org_uid,
            "org_uid": org_uid,
            "plant_uuid": sample_plant.get('plant_uuid', 'temp-uuid')
        }

        # Save to S3
        try:
            from json_s3_storage import store_hierarchical_organization_data
        except ImportError:
            from .json_s3_storage import store_hierarchical_organization_data

        org_s3_url = store_hierarchical_organization_data(
            org_data=org_data,
            plant_name=sample_plant.get('plant_name', 'Unknown Plant'),
            session_id=session_id,
            fallback_metadata=fallback_metadata
        )

        print(f"[Session {session_id}] ✅ Organization data saved to S3: {org_s3_url}")
        return org_s3_url or ""

    except Exception as e:
        print(f"[Session {session_id}] ❌ Failed to extract organization data: {e}")
        return ""


def process_single_plant_simple(plant: dict, org_uid: str, org_name: str, session_id: str) -> dict:
    """
    Process a single plant with plant + unit data + images (NO RECURSION)

    Args:
        plant: Plant data from database
        org_uid: Organization UID
        org_name: Organization name
        session_id: Session ID

    Returns:
        Dict with processing results
    """
    plant_name = plant.get('plant_name', 'Unknown Plant')
    plant_uuid = plant.get('plant_uuid', '')
    country = plant.get('country', 'Unknown')

    print(f"[Session {session_id}] 🏭 Processing plant: {plant_name}")

    try:
        # Step 1: Start image extraction in parallel
        import threading
        try:
            from image_extraction import extract_and_upload_images
        except ImportError:
            try:
                from .image_extraction import extract_and_upload_images
            except ImportError:
                # Fallback if image extraction not available
                def extract_and_upload_images(plant_name, session_id):
                    print(f"[Session {session_id}] ⚠️ Image extraction not available")
                    return []

        image_results = {"urls": [], "error": None}

        def extract_images():
            try:
                image_urls = extract_and_upload_images(plant_name, session_id)
                image_results["urls"] = image_urls
                print(f"[Session {session_id}] ✅ Images extracted: {len(image_urls)}")
            except Exception as e:
                image_results["error"] = str(e)
                print(f"[Session {session_id}] ❌ Image extraction failed: {e}")

        image_thread = threading.Thread(target=extract_images)
        image_thread.start()

        # Step 2: Create and save plant data
        try:
            from json_s3_storage import store_hierarchical_plant_data, store_hierarchical_unit_data
        except ImportError:
            from .json_s3_storage import store_hierarchical_plant_data, store_hierarchical_unit_data

        # Wait for images (with timeout)
        image_thread.join(timeout=120)  # 2 minutes max

        # Create plant data with images
        plant_data = {
            "sk": f"plant#{plant_name.lower().replace(' ', '_')}",
            "plant_name": plant_name,
            "plant_uuid": plant_uuid,
            "org_uid": org_uid,
            "country": country,
            "image_urls": image_results["urls"],
            "image_extraction_error": image_results["error"],
            "extraction_type": "multi_plant_processing",
            "timestamp": f"{__import__('datetime').datetime.now().isoformat()}Z"
        }

        # Prepare fallback metadata
        fallback_metadata = {
            "country": country,
            "org_uuid": org_uid,
            "org_uid": org_uid,
            "plant_uuid": plant_uuid
        }

        # Save plant data
        plant_s3_url = store_hierarchical_plant_data(
            plant_data=plant_data,
            plant_name=plant_name,
            session_id=session_id,
            fallback_metadata=fallback_metadata
        )

        # Step 3: Create and save unit data
        unit_s3_urls = []
        for unit_num in [1, 2]:  # Default units
            unit_data = {
                "sk": f"unit#{plant_name.lower().replace(' ', '_')}#{unit_num}",
                "unit_number": unit_num,
                "plant_name": plant_name,
                "plant_uuid": plant_uuid,
                "org_uid": org_uid,
                "extraction_type": "multi_plant_processing",
                "timestamp": f"{__import__('datetime').datetime.now().isoformat()}Z"
            }

            unit_s3_url = store_hierarchical_unit_data(
                unit_data=unit_data,
                plant_name=plant_name,
                session_id=session_id,
                fallback_metadata=fallback_metadata
            )

            if unit_s3_url:
                unit_s3_urls.append(unit_s3_url)

        return {
            "success": True,
            "plant_name": plant_name,
            "plant_uuid": plant_uuid,
            "org_uid": org_uid,
            "s3_urls": {
                "plant": plant_s3_url or "",
                "units": unit_s3_urls,
                "images": image_results["urls"]
            },
            "images_extracted": len(image_results["urls"]),
            "session_id": session_id
        }

    except Exception as e:
        print(f"[Session {session_id}] ❌ Failed to process plant {plant_name}: {e}")
        return {
            "success": False,
            "plant_name": plant_name,
            "plant_uuid": plant_uuid,
            "org_uid": org_uid,
            "error": str(e),
            "session_id": session_id
        }
